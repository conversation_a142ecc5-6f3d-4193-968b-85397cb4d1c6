package com.facishare.paas.metadata.dataloader.image.util;

import com.facishare.paas.metadata.dataloader.image.model.ImageUploadResult;
import com.facishare.paas.metadata.dataloader.util.I18NKey;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 图片错误映射工具类
 * 负责将具体的图片处理错误映射到对应的多语key
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class ImageErrorMapper {
    
    /**
     * 将图片上传结果错误映射到多语key
     * 
     * @param result 图片上传结果
     * @return 对应的多语key
     */
    public static String mapUploadErrorToI18nKey(ImageUploadResult result) {
        if (result == null || result.isSuccess()) {
            return null;
        }
        
        String errorMessage = result.getErrorMessage();
        if (StringUtils.isBlank(errorMessage)) {
            return I18NKey.IMAGE_UPLOAD_FAILED;
        }
        
        String lowerErrorMsg = errorMessage.toLowerCase();
        
        // 数据为空错误
        if (lowerErrorMsg.contains("null") || lowerErrorMsg.contains("empty")) {
            return I18NKey.IMAGE_DATA_EMPTY;
        }
        
        // 文件大小超限错误
        if (lowerErrorMsg.contains("size") || lowerErrorMsg.contains("limit") || 
            lowerErrorMsg.contains("large") || lowerErrorMsg.contains("exceed")) {
            return I18NKey.IMAGE_SIZE_EXCEEDED;
        }
        
        // 格式不支持错误
        if (lowerErrorMsg.contains("format") || lowerErrorMsg.contains("unsupported") || 
            lowerErrorMsg.contains("invalid")) {
            return I18NKey.IMAGE_FORMAT_UNSUPPORTED;
        }
        
        // 网络相关错误
        if (lowerErrorMsg.contains("network") || lowerErrorMsg.contains("timeout") || 
            lowerErrorMsg.contains("connection") || lowerErrorMsg.contains("connect")) {
            return I18NKey.IMAGE_UPLOAD_FAILED;
        }
        
        // 服务器错误
        if (lowerErrorMsg.contains("server") || lowerErrorMsg.contains("500") || 
            lowerErrorMsg.contains("503") || lowerErrorMsg.contains("502")) {
            return I18NKey.IMAGE_UPLOAD_FAILED;
        }
        
        // 默认上传失败
        return I18NKey.IMAGE_UPLOAD_FAILED;
    }
    
    /**
     * 将异常映射到多语key
     * 
     * @param exception 异常对象
     * @return 对应的多语key
     */
    public static String mapExceptionToI18nKey(Exception exception) {
        if (exception == null) {
            return I18NKey.IMAGE_PROCESSING_ERROR;
        }
        
        String exceptionMsg = exception.getMessage();
        if (StringUtils.isBlank(exceptionMsg)) {
            return I18NKey.IMAGE_PROCESSING_ERROR;
        }
        
        String lowerExceptionMsg = exceptionMsg.toLowerCase();
        
        // 文件读取相关错误
        if (lowerExceptionMsg.contains("file") || lowerExceptionMsg.contains("zip") || 
            lowerExceptionMsg.contains("read") || lowerExceptionMsg.contains("io")) {
            return I18NKey.IMAGE_EXTRACTION_FAILED;
        }
        
        // 图片不存在错误
        if (lowerExceptionMsg.contains("not found") || lowerExceptionMsg.contains("missing") || 
            lowerExceptionMsg.contains("no image")) {
            return I18NKey.IMAGE_NOT_FOUND;
        }
        
        // XML解析错误
        if (lowerExceptionMsg.contains("xml") || lowerExceptionMsg.contains("parse") || 
            lowerExceptionMsg.contains("dispimg")) {
            return I18NKey.IMAGE_EXTRACTION_FAILED;
        }
        
        // 默认处理错误
        return I18NKey.IMAGE_PROCESSING_ERROR;
    }
    
    /**
     * 格式化错误标识
     * 
     * @param i18nKey 多语key
     * @param originalValue 原始单元格值
     * @return 格式化的错误标识字符串
     */
    public static String formatErrorTag(String i18nKey, String originalValue) {
        if (StringUtils.isBlank(i18nKey)) {
            return originalValue;
        }
        
        return String.format("[IMG_ERROR:%s]%s", i18nKey, originalValue != null ? originalValue : "");
    }
    
    /**
     * 检查字符串是否包含图片错误标识
     * 
     * @param cellValue 单元格值
     * @return 如果包含错误标识返回true
     */
    public static boolean hasImageErrorTag(String cellValue) {
        return StringUtils.isNotBlank(cellValue) && cellValue.startsWith("[IMG_ERROR:");
    }
    
    /**
     * 从错误标识中提取多语key
     * 
     * @param cellValue 包含错误标识的单元格值
     * @return 多语key，如果解析失败返回null
     */
    public static String extractI18nKey(String cellValue) {
        if (!hasImageErrorTag(cellValue)) {
            return null;
        }
        
        int startIndex = "[IMG_ERROR:".length();
        int endIndex = cellValue.indexOf(']', startIndex);
        
        if (endIndex == -1) {
            return null;
        }
        
        return cellValue.substring(startIndex, endIndex);
    }
    
    /**
     * 从错误标识中提取原始值
     * 
     * @param cellValue 包含错误标识的单元格值
     * @return 原始值，如果解析失败返回原字符串
     */
    public static String extractOriginalValue(String cellValue) {
        if (!hasImageErrorTag(cellValue)) {
            return cellValue;
        }
        
        int endIndex = cellValue.indexOf(']');
        if (endIndex == -1) {
            return cellValue;
        }
        
        return cellValue.substring(endIndex + 1);
    }
    
    /**
     * 清理单元格值中的错误标识，返回原始值
     * 
     * @param cellValue 可能包含错误标识的单元格值
     * @return 清理后的原始值
     */
    public static String cleanErrorTag(String cellValue) {
        return extractOriginalValue(cellValue);
    }
}
