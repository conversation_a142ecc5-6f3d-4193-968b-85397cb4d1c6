package com.facishare.paas.metadata.dataloader.image.model;

import com.facishare.paas.metadata.dataloader.util.I18NKey;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 图片上传结果
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
public class ImageUploadResult {
    
    /**
     * 是否上传成功
     */
    @Builder.Default
    private boolean success = false;
    
    /**
     * 上传后的文件路径
     */
    private String uploadedPath;
    
    /**
     * 原始文件名
     */
    private String originalFileName;
    
    /**
     * 文件大小（字节）
     */
    @Builder.Default
    private long fileSize = 0;
    
    /**
     * 图片格式
     */
    private String format;
    
    /**
     * 上传耗时（毫秒）
     */
    @Builder.Default
    private long uploadDuration = 0;
    
    /**
     * 错误信息（上传失败时）
     */
    private String errorMessage;
    
    /**
     * 重试次数
     */
    @Builder.Default
    private int retryCount = 0;
    
    /**
     * 创建成功的上传结果
     * 
     * @param uploadedPath 上传后的文件路径
     * @param originalFileName 原始文件名
     * @param fileSize 文件大小
     * @return 成功的上传结果
     */
    public static ImageUploadResult success(String uploadedPath, String originalFileName, long fileSize) {
        return ImageUploadResult.builder()
            .success(true)
            .uploadedPath(uploadedPath)
            .originalFileName(originalFileName)
            .fileSize(fileSize)
            .build();
    }
    
    /**
     * 创建失败的上传结果
     * 
     * @param errorMessage 错误信息
     * @param originalFileName 原始文件名
     * @return 失败的上传结果
     */
    public static ImageUploadResult failure(String errorMessage, String originalFileName) {
        return ImageUploadResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .originalFileName(originalFileName)
            .build();
    }
    
    /**
     * 获取格式化的文件大小
     * 
     * @return 格式化的大小字符串
     */
    public String getFormattedFileSize() {
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 获取上传速度（KB/s）
     * 
     * @return 上传速度
     */
    public double getUploadSpeed() {
        if (uploadDuration <= 0 || fileSize <= 0) {
            return 0.0;
        }
        
        return (fileSize / 1024.0) / (uploadDuration / 1000.0);
    }
    
    /**
     * 获取格式化的上传速度
     * 
     * @return 格式化的上传速度字符串
     */
    public String getFormattedUploadSpeed() {
        double speed = getUploadSpeed();
        if (speed < 1024) {
            return String.format("%.1f KB/s", speed);
        } else {
            return String.format("%.1f MB/s", speed / 1024.0);
        }
    }
    
    /**
     * 检查是否需要重试
     * 
     * @param maxRetries 最大重试次数
     * @return 如果需要重试返回true
     */
    public boolean shouldRetry(int maxRetries) {
        return !success && retryCount < maxRetries;
    }
    
    /**
     * 增加重试次数
     * 
     * @return 更新后的结果
     */
    public ImageUploadResult incrementRetry() {
        this.retryCount++;
        return this;
    }
    
    /**
     * 获取结果摘要
     * 
     * @return 结果摘要字符串
     */
    public String getSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("ImageUploadResult[");
        sb.append("success=").append(success);
        
        if (success) {
            sb.append(", path=").append(uploadedPath);
            sb.append(", size=").append(getFormattedFileSize());
            sb.append(", duration=").append(uploadDuration).append("ms");
            
            if (uploadDuration > 0) {
                sb.append(", speed=").append(getFormattedUploadSpeed());
            }
        } else {
            sb.append(", error=").append(errorMessage);
            
            if (retryCount > 0) {
                sb.append(", retries=").append(retryCount);
            }
        }
        
        if (originalFileName != null) {
            sb.append(", file=").append(originalFileName);
        }
        
        sb.append("]");
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return getSummary();
    }

    /**
     * 获取对应的多语key
     * 根据错误信息智能映射到合适的多语key
     *
     * @return 多语key，成功时返回null
     */
    public String getI18nKey() {
        if (success) {
            return null;
        }

        if (StringUtils.isBlank(errorMessage)) {
            return I18NKey.IMAGE_UPLOAD_FAILED;
        }

        String lowerErrorMsg = errorMessage.toLowerCase();

        // 数据为空错误
        if (lowerErrorMsg.contains("null") || lowerErrorMsg.contains("empty")) {
            return I18NKey.IMAGE_DATA_EMPTY;
        }

        // 文件大小超限错误
        if (lowerErrorMsg.contains("size") || lowerErrorMsg.contains("limit") ||
            lowerErrorMsg.contains("large") || lowerErrorMsg.contains("exceed")) {
            return I18NKey.IMAGE_MAX_SIZE;
        }

        // 格式不支持错误
        if (lowerErrorMsg.contains("format") || lowerErrorMsg.contains("unsupported") ||
            lowerErrorMsg.contains("invalid")) {
            return I18NKey.IMAGE_FORMAT_UNSUPPORTED;
        }

        // 默认上传失败
        return I18NKey.IMAGE_UPLOAD_FAILED;
    }

    /**
     * 格式化错误标识
     * 将失败结果格式化为包含多语key的错误标识
     *
     * @param originalValue 原始单元格值
     * @return 格式化的错误标识字符串，成功时返回null
     */
    public String formatErrorTag(String originalValue) {
        if (success) {
            return null;
        }

        String i18nKey = getI18nKey();
        return String.format("[IMG_ERROR:%s]%s", i18nKey, originalValue != null ? originalValue : "");
    }

    /**
     * 检查字符串是否包含图片错误标识
     *
     * @param cellValue 单元格值
     * @return 如果包含错误标识返回true
     */
    public static boolean hasImageErrorTag(String cellValue) {
        return StringUtils.isNotBlank(cellValue) && cellValue.startsWith("[IMG_ERROR:");
    }

    /**
     * 从错误标识中提取多语key
     *
     * @param cellValue 包含错误标识的单元格值
     * @return 多语key，如果解析失败返回null
     */
    public static String extractI18nKey(String cellValue) {
        if (!hasImageErrorTag(cellValue)) {
            return null;
        }

        int startIndex = "[IMG_ERROR:".length();
        int endIndex = cellValue.indexOf(']', startIndex);

        if (endIndex == -1) {
            return null;
        }

        return cellValue.substring(startIndex, endIndex);
    }

    /**
     * 从错误标识中提取原始值
     *
     * @param cellValue 包含错误标识的单元格值
     * @return 原始值，如果解析失败返回原字符串
     */
    public static String extractOriginalValue(String cellValue) {
        if (!hasImageErrorTag(cellValue)) {
            return cellValue;
        }

        int endIndex = cellValue.indexOf(']');
        if (endIndex == -1) {
            return cellValue;
        }

        return cellValue.substring(endIndex + 1);
    }

    /**
     * 清理单元格值中的错误标识，返回原始值
     *
     * @param cellValue 可能包含错误标识的单元格值
     * @return 清理后的原始值
     */
    public static String cleanErrorTag(String cellValue) {
        return extractOriginalValue(cellValue);
    }
}
