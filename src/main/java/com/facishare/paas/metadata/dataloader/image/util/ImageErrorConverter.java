package com.facishare.paas.metadata.dataloader.image.util;

import com.facishare.paas.metadata.dataloader.image.collector.ImageProcessingErrorCollector;
import com.facishare.paas.metadata.dataloader.image.model.ImageProcessingError;
import com.facishare.paas.metadata.dataloader.service.IBulkImportRestService;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 图片错误转换工具类
 * 负责将图片处理错误转换为系统标准的RowError格式
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class ImageErrorConverter {
    
    /**
     * 将图片处理错误转换为RowError列表
     * 
     * @param errorCollector 错误收集器
     * @param apiName API名称
     * @return RowError列表
     */
    public static List<IBulkImportRestService.BulkInsertResult.RowError> convertToRowErrors(
            ImageProcessingErrorCollector errorCollector, String apiName) {
        
        if (errorCollector == null || !errorCollector.hasAnyErrors()) {
            return new ArrayList<>();
        }
        
        List<IBulkImportRestService.BulkInsertResult.RowError> rowErrors = new ArrayList<>();
        
        // 按行分组处理错误
        Map<Integer, List<ImageProcessingError>> errorsByRow = errorCollector.getAllErrors()
                .stream()
                .collect(Collectors.groupingBy(ImageProcessingError::getRowIndex));
        
        for (Map.Entry<Integer, List<ImageProcessingError>> entry : errorsByRow.entrySet()) {
            int rowIndex = entry.getKey();
            List<ImageProcessingError> rowImageErrors = entry.getValue();
            
            // 为每行创建一个RowError
            String combinedErrorMessage = buildCombinedErrorMessage(rowImageErrors);
            
            IBulkImportRestService.BulkInsertResult.RowError rowError = 
                    IBulkImportRestService.BulkInsertResult.RowError.builder()
                    .rowNo(rowIndex + 2) // Excel行号从2开始（第1行是表头）
                    .errorMessage(combinedErrorMessage)
                    .objectApiName(apiName)
                    .build();
            
            rowErrors.add(rowError);
            
            log.debug("Converted {} image errors for row {} to RowError: {}", 
                    rowImageErrors.size(), rowIndex + 2, combinedErrorMessage);
        }
        
        log.info("Converted {} image processing errors to {} RowErrors for API: {}", 
                errorCollector.getTotalErrorCount(), rowErrors.size(), apiName);
        
        return rowErrors;
    }
    
    /**
     * 构建组合错误消息
     * 
     * @param imageErrors 图片错误列表
     * @return 组合的错误消息
     */
    private static String buildCombinedErrorMessage(List<ImageProcessingError> imageErrors) {
        if (imageErrors.isEmpty()) {
            return "Unknown image processing error";
        }
        
        if (imageErrors.size() == 1) {
            return imageErrors.get(0).getFormattedErrorMessage();
        }
        
        // 多个错误时，按字段分组
        Map<String, List<ImageProcessingError>> errorsByField = imageErrors.stream()
                .collect(Collectors.groupingBy(ImageProcessingError::getFieldName));
        
        StringBuilder sb = new StringBuilder();
        sb.append("Multiple image processing errors: ");
        
        for (Map.Entry<String, List<ImageProcessingError>> entry : errorsByField.entrySet()) {
            String fieldName = entry.getKey();
            List<ImageProcessingError> fieldErrors = entry.getValue();
            
            sb.append("[").append(fieldName).append(": ");
            
            if (fieldErrors.size() == 1) {
                sb.append(fieldErrors.get(0).getShortErrorMessage());
            } else {
                // 同一字段多个错误，只显示主要错误类型
                Map<ImageProcessingError.ErrorType, Long> errorTypeCounts = fieldErrors.stream()
                        .collect(Collectors.groupingBy(ImageProcessingError::getErrorType, Collectors.counting()));
                
                errorTypeCounts.forEach((type, count) -> {
                    sb.append(type.getDescription());
                    if (count > 1) {
                        sb.append("(").append(count).append(")");
                    }
                    sb.append(", ");
                });
                
                // 移除最后的逗号和空格
                if (sb.length() > 2) {
                    sb.setLength(sb.length() - 2);
                }
            }
            
            sb.append("] ");
        }
        
        return sb.toString().trim();
    }
    
    /**
     * 合并图片错误和CRM错误
     * 
     * @param imageErrorCollector 图片错误收集器
     * @param crmResult CRM导入结果
     * @param apiName API名称
     * @return 合并后的结果
     */
    public static IBulkImportRestService.BulkInsertResult mergeImageErrorsWithCrmResult(
            ImageProcessingErrorCollector imageErrorCollector,
            IBulkImportRestService.BulkInsertResult crmResult,
            String apiName) {
        
        if (imageErrorCollector == null || !imageErrorCollector.hasAnyErrors()) {
            return crmResult; // 没有图片错误，直接返回CRM结果
        }
        
        // 转换图片错误为RowError
        List<IBulkImportRestService.BulkInsertResult.RowError> imageRowErrors = 
                convertToRowErrors(imageErrorCollector, apiName);
        
        if (imageRowErrors.isEmpty()) {
            return crmResult; // 没有转换出错误，直接返回CRM结果
        }
        
        // 如果CRM结果为空或失败，创建新的结果
        if (crmResult == null || !crmResult.isSuccess()) {
            return createImageErrorResult(imageRowErrors, crmResult);
        }
        
        // 合并CRM错误和图片错误
        IBulkImportRestService.BulkInsertResult.Value originalValue = crmResult.getValue();
        List<IBulkImportRestService.BulkInsertResult.RowError> allRowErrors = new ArrayList<>();
        
        // 添加原有的CRM错误
        if (originalValue != null && originalValue.getRowErrorList() != null) {
            allRowErrors.addAll(originalValue.getRowErrorList());
        }
        
        // 添加图片错误（避免重复）
        for (IBulkImportRestService.BulkInsertResult.RowError imageError : imageRowErrors) {
            boolean isDuplicate = allRowErrors.stream()
                    .anyMatch(existingError -> existingError.getRowNo() == imageError.getRowNo());
            
            if (!isDuplicate) {
                allRowErrors.add(imageError);
            } else {
                // 如果行号重复，合并错误消息
                allRowErrors.stream()
                        .filter(existingError -> existingError.getRowNo() == imageError.getRowNo())
                        .findFirst()
                        .ifPresent(existingError -> {
                            String combinedMessage = existingError.getErrorMessage() + "; " + imageError.getErrorMessage();
                            existingError.setErrorMessage(combinedMessage);
                        });
            }
        }
        
        // 创建新的Value对象
        IBulkImportRestService.BulkInsertResult.Value newValue = 
                IBulkImportRestService.BulkInsertResult.Value.builder()
                .importSucceedCount(originalValue != null ? originalValue.getImportSucceedCount() : 0)
                .rowErrorList(allRowErrors)
                .build();
        
        // 创建新的结果对象
        return IBulkImportRestService.BulkInsertResult.builder()
                .success(crmResult.isSuccess())
                .message(crmResult.getMessage())
                .errorCode(crmResult.getErrorCode())
                .value(newValue)
                .hasImportPreProcessingFunction(crmResult.getHasImportPreProcessingFunction())
                .build();
    }
    
    /**
     * 创建仅包含图片错误的结果
     * 
     * @param imageRowErrors 图片错误列表
     * @param originalResult 原始结果（可能为null）
     * @return 包含图片错误的结果
     */
    private static IBulkImportRestService.BulkInsertResult createImageErrorResult(
            List<IBulkImportRestService.BulkInsertResult.RowError> imageRowErrors,
            IBulkImportRestService.BulkInsertResult originalResult) {
        
        IBulkImportRestService.BulkInsertResult.Value value = 
                IBulkImportRestService.BulkInsertResult.Value.builder()
                .importSucceedCount(0)
                .rowErrorList(imageRowErrors)
                .build();
        
        String message = "Image processing errors occurred";
        if (originalResult != null && originalResult.getMessage() != null) {
            message = originalResult.getMessage() + "; " + message;
        }
        
        return IBulkImportRestService.BulkInsertResult.builder()
                .success(false) // 有图片错误就认为不成功
                .message(message)
                .errorCode(originalResult != null ? originalResult.getErrorCode() : -1)
                .value(value)
                .hasImportPreProcessingFunction(originalResult != null ? originalResult.getHasImportPreProcessingFunction() : null)
                .build();
    }
}
